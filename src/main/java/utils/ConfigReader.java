package utils;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Properties;

/**
 * ConfigReader class to read configuration properties
 */
public class ConfigReader {
    
    private static Properties properties;
    private static final String CONFIG_FILE_PATH = "src/test/resources/config.properties";
    
    static {
        loadProperties();
    }
    
    /**
     * Load properties from config file
     */
    private static void loadProperties() {
        try {
            properties = new Properties();
            FileInputStream fileInputStream = new FileInputStream(CONFIG_FILE_PATH);
            properties.load(fileInputStream);
            fileInputStream.close();
        } catch (IOException e) {
            // If config file doesn't exist, use default values
            System.out.println("Config file not found. Using default values.");
            properties = new Properties();
            setDefaultProperties();
        }
    }
    
    /**
     * Set default properties if config file is not available
     */
    private static void setDefaultProperties() {
        properties.setProperty("base.url", "https://www.wethinkcode.co.za/");
        properties.setProperty("browser", "chrome");
        properties.setProperty("timeout", "10");
        properties.setProperty("environment", "prod");
    }
    
    /**
     * Get property value by key
     * @param key - property key
     * @return property value
     */
    public static String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * Get base URL
     */
    public static String getBaseUrl() {
        return getProperty("base.url");
    }
    
    /**
     * Get browser type
     */
    public static String getBrowser() {
        String browser = System.getProperty("browser");
        return (browser != null && !browser.trim().isEmpty()) ? browser : getProperty("browser");
    }
    
    /**
     * Get timeout value
     */
    public static int getTimeout() {
        try {
            return Integer.parseInt(getProperty("timeout"));
        } catch (NumberFormatException e) {
            return 10; // default timeout
        }
    }
    
    /**
     * Get environment
     */
    public static String getEnvironment() {
        String environment = System.getProperty("environment");
        return (environment != null && !environment.trim().isEmpty()) ? environment : getProperty("environment");
    }
}
