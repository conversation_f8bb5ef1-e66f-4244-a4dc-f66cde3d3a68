package utils;

import org.apache.commons.io.FileUtils;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * ScreenshotUtils class for capturing and managing screenshots during test execution
 */
public class ScreenshotUtils {
    
    private static final String SCREENSHOT_DIR = "screenshots";
    private static final String DATE_FORMAT = "yyyy-MM-dd_HH-mm-ss";
    
    static {
        // Create screenshots directory if it doesn't exist
        createScreenshotDirectory();
    }
    
    /**
     * Create screenshots directory
     */
    private static void createScreenshotDirectory() {
        File screenshotDir = new File(SCREENSHOT_DIR);
        if (!screenshotDir.exists()) {
            screenshotDir.mkdirs();
            System.out.println("Created screenshots directory: " + screenshotDir.getAbsolutePath());
        }
    }
    
    /**
     * Take a screenshot with a custom name
     * @param testName - name of the test
     * @param stepName - name of the step
     * @return screenshot file path
     */
    public static String takeScreenshot(String testName, String stepName) {
        WebDriver driver = DriverFactory.getDriver();
        if (driver == null) {
            System.out.println("Driver is null, cannot take screenshot");
            return null;
        }

        try {
            TakesScreenshot takesScreenshot = (TakesScreenshot) driver;
            File sourceFile = takesScreenshot.getScreenshotAs(OutputType.FILE);

            // Create subdirectory for the test
            File testDir = new File(SCREENSHOT_DIR, testName);
            if (!testDir.exists()) {
                testDir.mkdirs();
                System.out.println("Created test directory: " + testDir.getAbsolutePath());
            }

            String timestamp = new SimpleDateFormat(DATE_FORMAT).format(new Date());
            String fileName = String.format("%s_%s.png", stepName, timestamp);
            File destFile = new File(testDir, fileName);

            FileUtils.copyFile(sourceFile, destFile);

            String relativePath = destFile.getPath();
            System.out.println("📸 Screenshot captured: " + relativePath);
            return relativePath;

        } catch (IOException e) {
            System.out.println("Failed to take screenshot: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Take a screenshot with automatic naming based on current test
     * @param stepName - name of the step
     * @return screenshot file path
     */
    public static String takeScreenshot(String stepName) {
        // Get current test method name from stack trace
        String testName = getCurrentTestMethodName();
        return takeScreenshot(testName, stepName);
    }
    
    /**
     * Take a screenshot on test failure
     * @param testName - name of the failed test
     * @return screenshot file path
     */
    public static String takeFailureScreenshot(String testName) {
        return takeScreenshot(testName, "FAILURE");
    }
    
    /**
     * Take a screenshot with just timestamp
     * @return screenshot file path
     */
    public static String takeScreenshot() {
        String timestamp = new SimpleDateFormat(DATE_FORMAT).format(new Date());
        return takeScreenshot("general", timestamp);
    }
    
    /**
     * Get current test method name from stack trace
     * @return test method name
     */
    private static String getCurrentTestMethodName() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement element : stackTrace) {
            String className = element.getClassName();
            String methodName = element.getMethodName();
            
            // Look for test methods (usually start with 'test' or have Test annotation)
            if (className.contains("Test") && (methodName.startsWith("test") || methodName.contains("Test"))) {
                return methodName;
            }
        }
        return "unknown_test";
    }
    
    /**
     * Clean up old screenshots (older than specified days)
     * @param daysToKeep - number of days to keep screenshots
     */
    public static void cleanupOldScreenshots(int daysToKeep) {
        File screenshotDir = new File(SCREENSHOT_DIR);
        if (!screenshotDir.exists()) {
            return;
        }

        long cutoffTime = System.currentTimeMillis() - (daysToKeep * 24 * 60 * 60 * 1000L);
        cleanupDirectory(screenshotDir, cutoffTime);
    }

    /**
     * Recursively clean up old files in directory and subdirectories
     * @param directory - directory to clean
     * @param cutoffTime - time cutoff for deletion
     */
    private static void cleanupDirectory(File directory, long cutoffTime) {
        File[] files = directory.listFiles();
        if (files != null) {
            int deletedCount = 0;
            for (File file : files) {
                if (file.isDirectory()) {
                    // Recursively clean subdirectories
                    cleanupDirectory(file, cutoffTime);
                    // Remove empty directories
                    if (file.list() != null && file.list().length == 0) {
                        file.delete();
                    }
                } else if (file.isFile() && file.lastModified() < cutoffTime) {
                    if (file.delete()) {
                        deletedCount++;
                    }
                }
            }
            if (deletedCount > 0) {
                System.out.println("Cleaned up " + deletedCount + " old screenshots from " + directory.getName());
            }
        }
    }
    
    /**
     * Get screenshots directory path
     * @return screenshots directory path
     */
    public static String getScreenshotDirectory() {
        return new File(SCREENSHOT_DIR).getAbsolutePath();
    }
}
