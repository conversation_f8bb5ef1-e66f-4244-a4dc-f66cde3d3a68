package tests;

import org.testng.Assert;
import org.testng.annotations.Test;
import pages.HomePage;
import utils.ScreenshotUtils;

/**
 * SmokeTests class containing basic smoke tests for WeThinkCode website
 */
public class SmokeTests extends BaseTest {
    
    @Test(priority = 1, description = "Verify homepage loads successfully")
    public void testHomepageLoads() {
        HomePage homePage = new HomePage();

        // Take screenshot of initial page load
        ScreenshotUtils.takeScreenshot("testHomepageLoads", "initial_page_load");

        // Verify homepage is loaded
        Assert.assertTrue(homePage.isHomePageLoaded(),
            "Homepage should load successfully");

        // Verify page title is not empty
        String pageTitle = homePage.getPageTitle();
        Assert.assertNotNull(pageTitle, "Page title should not be null");
        Assert.assertFalse(pageTitle.isEmpty(), "Page title should not be empty");

        // Take screenshot after validation
        ScreenshotUtils.takeScreenshot("testHomepageLoads", "validation_complete");

        System.out.println("✅ Homepage loaded successfully with title: " + pageTitle);
    }
    
    @Test(priority = 2, description = "Verify key homepage elements are displayed")
    public void testHomepageElements() {
        HomePage homePage = new HomePage();

        // Take screenshot before element validation
        ScreenshotUtils.takeScreenshot("testHomepageElements", "before_validation");

        // Verify navigation menu is displayed
        Assert.assertTrue(homePage.isNavigationMenuDisplayed(),
            "Navigation menu should be displayed");

        // Verify hero section is displayed
        boolean heroDisplayed = homePage.isHeroSectionDisplayed();
        if (heroDisplayed) {
            System.out.println("✅ Hero section is displayed");
        } else {
            System.out.println("⚠️ Hero section not found - this might be expected based on site design");
        }

        // Verify footer is displayed
        boolean footerDisplayed = homePage.isFooterDisplayed();
        if (footerDisplayed) {
            System.out.println("✅ Footer is displayed");
        } else {
            System.out.println("⚠️ Footer not found - this might be expected based on site design");
        }

        // Take screenshot after element validation
        ScreenshotUtils.takeScreenshot("testHomepageElements", "after_validation");

        System.out.println("✅ Key homepage elements validation completed");
    }
    
    @Test(priority = 3, description = "Verify Apply Now navigation")
    public void testApplyNowNavigation() {
        HomePage homePage = new HomePage();

        // Take screenshot before navigation
        ScreenshotUtils.takeScreenshot("testApplyNowNavigation", "before_click");

        // Get current URL before clicking
        String initialUrl = homePage.getCurrentUrl();
        System.out.println("Initial URL: " + initialUrl);

        // Click Apply Now button
        homePage.clickApplyNow();

        // Wait a moment for navigation
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Take screenshot after navigation attempt
        ScreenshotUtils.takeScreenshot("testApplyNowNavigation", "after_click");

        // Get URL after clicking
        String currentUrl = homePage.getCurrentUrl();
        System.out.println("URL after clicking Apply Now: " + currentUrl);

        // Verify navigation occurred (URL should change or contain apply-related path)
        boolean navigationOccurred = !currentUrl.equals(initialUrl) ||
                                   currentUrl.contains("apply") ||
                                   currentUrl.contains("application");

        if (navigationOccurred) {
            System.out.println("✅ Apply Now navigation successful");
        } else {
            System.out.println("⚠️ Apply Now navigation might not have occurred - checking page content");
            // This is not necessarily a failure as the button might open a modal or scroll to a section
        }
    }
    
    @Test(priority = 4, description = "Verify About page navigation")
    public void testAboutNavigation() {
        HomePage homePage = new HomePage();
        
        // Get current URL before clicking
        String initialUrl = homePage.getCurrentUrl();
        System.out.println("Initial URL: " + initialUrl);
        
        // Click About link
        homePage.clickAbout();
        
        // Wait a moment for navigation
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Get URL after clicking
        String currentUrl = homePage.getCurrentUrl();
        System.out.println("URL after clicking About: " + currentUrl);
        
        // Verify navigation occurred
        boolean navigationOccurred = !currentUrl.equals(initialUrl) || 
                                   currentUrl.contains("about");
        
        if (navigationOccurred) {
            System.out.println("✅ About navigation successful");
        } else {
            System.out.println("⚠️ About navigation might not have occurred");
        }
    }
    
    @Test(priority = 5, description = "Verify Contact page navigation")
    public void testContactNavigation() {
        HomePage homePage = new HomePage();
        
        // Get current URL before clicking
        String initialUrl = homePage.getCurrentUrl();
        System.out.println("Initial URL: " + initialUrl);
        
        // Click Contact link
        homePage.clickContact();
        
        // Wait a moment for navigation
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Get URL after clicking
        String currentUrl = homePage.getCurrentUrl();
        System.out.println("URL after clicking Contact: " + currentUrl);
        
        // Verify navigation occurred
        boolean navigationOccurred = !currentUrl.equals(initialUrl) || 
                                   currentUrl.contains("contact");
        
        if (navigationOccurred) {
            System.out.println("✅ Contact navigation successful");
        } else {
            System.out.println("⚠️ Contact navigation might not have occurred");
        }
    }
}
