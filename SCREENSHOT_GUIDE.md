# Screenshot Functionality Guide

## Overview

The WeThinkCode Test Automation Framework now includes comprehensive screenshot functionality to capture browser simulations during test execution. Screenshots are automatically organized in test-specific subdirectories for better organization and debugging.

## Features

### 📸 **Automatic Screenshot Capture**
- Screenshots are captured at key test steps
- Organized in subdirectories by test name
- Timestamped for easy identification
- Automatic failure screenshots

### 🗂️ **Organized Structure**
```
screenshots/
├── testHomepageLoads/
│   ├── initial_page_load_2025-09-11_10-47-43.png
│   └── validation_complete_2025-09-11_10-47-43.png
├── testHomepageElements/
│   ├── before_validation_2025-09-11_10-44-18.png
│   └── after_validation_2025-09-11_10-44-18.png
└── testApplyNowNavigation/
    ├── before_click_2025-09-11_10-45-30.png
    └── after_click_2025-09-11_10-45-32.png
```

## Usage

### 🔧 **In Test Classes**

```java
import utils.ScreenshotUtils;

@Test
public void testExample() {
    // Take screenshot at specific step
    ScreenshotUtils.takeScreenshot("testExample", "step_description");
    
    // Perform test actions
    // ...
    
    // Take another screenshot
    ScreenshotUtils.takeScreenshot("testExample", "after_action");
}
```

### 📋 **Available Methods**

#### 1. **Test-Specific Screenshots**
```java
// Creates: screenshots/testName/stepName_timestamp.png
ScreenshotUtils.takeScreenshot("testName", "stepName");
```

#### 2. **Auto-Named Screenshots**
```java
// Automatically detects test method name
ScreenshotUtils.takeScreenshot("stepName");
```

#### 3. **Failure Screenshots**
```java
// Automatically called on test failures
ScreenshotUtils.takeFailureScreenshot("testName");
```

#### 4. **General Screenshots**
```java
// Creates: screenshots/general/timestamp.png
ScreenshotUtils.takeScreenshot();
```

### 🧹 **Cleanup Functionality**

```java
// Clean up screenshots older than 7 days
ScreenshotUtils.cleanupOldScreenshots(7);
```

## Current Implementation

### ✅ **Smoke Tests Integration**

The smoke tests now include screenshot capture at:

1. **testHomepageLoads**
   - `initial_page_load` - When page first loads
   - `validation_complete` - After all validations pass

2. **testHomepageElements**
   - `before_validation` - Before checking elements
   - `after_validation` - After element validation

3. **testApplyNowNavigation**
   - `before_click` - Before clicking Apply Now
   - `after_click` - After navigation attempt

### 🚨 **Automatic Failure Screenshots**

The `BaseTest` class automatically captures screenshots when tests fail:
- Screenshot saved as `screenshots/{testName}/FAILURE_{timestamp}.png`
- Includes test failure information in console output

## Running Tests with Screenshots

### 🖥️ **Headless Mode (Current)**
```bash
mvn test -Dtest=SmokeTests#testHomepageLoads -Dbrowser=chrome -Dheadless=true
```

### 🌐 **Visible Browser Mode**
```bash
mvn test -Dtest=SmokeTests#testHomepageLoads -Dbrowser=chrome -Dheadless=false
```

## Configuration

### 📁 **Screenshot Directory**
- Default: `screenshots/` (relative to project root)
- Automatically created if doesn't exist
- Ignored by Git (configured in `.gitignore`)

### 🕐 **Timestamp Format**
- Format: `yyyy-MM-dd_HH-mm-ss`
- Example: `2025-09-11_10-47-43`

### 🗃️ **File Naming Convention**
- Pattern: `{stepName}_{timestamp}.png`
- Example: `initial_page_load_2025-09-11_10-47-43.png`

## Benefits

### 🔍 **Debugging**
- Visual confirmation of test execution
- Easy identification of failure points
- Step-by-step test progression

### 📊 **Documentation**
- Visual test evidence
- Regression testing comparison
- Test result reporting

### 🎯 **Quality Assurance**
- Verify UI elements are displayed correctly
- Confirm navigation and interactions
- Validate responsive design

## Example Output

When running tests, you'll see console output like:
```
📸 Screenshot captured: screenshots/testHomepageLoads/initial_page_load_2025-09-11_10-47-43.png
✅ Homepage loaded successfully with title: Home - WeThinkCode_
📸 Screenshot captured: screenshots/testHomepageLoads/validation_complete_2025-09-11_10-47-43.png
```

## Next Steps

1. **Run smoke tests** to generate screenshots
2. **Review captured images** to verify test execution
3. **Customize screenshot points** in your tests as needed
4. **Set up cleanup schedule** for old screenshots

The screenshot functionality provides valuable visual feedback for your browser automation tests, making debugging and verification much easier!
