<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "http://testng.org/testng-1.0.dtd">

<suite name="WeThinkCode Test Suite" verbose="1">

    <!-- Framework Validation Tests (No Browser Required) -->
    <test name="Framework Validation Tests" preserve-order="true">
        <classes>
            <class name="tests.ConfigurationTests"/>
            <class name="tests.FrameworkValidationTests"/>
        </classes>
    </test>

    <!-- Chrome Browser Tests -->
    <test name="Chrome Smoke Tests" preserve-order="true" enabled="true">
        <parameter name="browser" value="chrome"/>
        <classes>
            <class name="tests.SmokeTests"/>
        </classes>
    </test>

    <!-- Firefox Browser Tests -->
    <test name="Firefox Smoke Tests" preserve-order="true" enabled="true">
        <parameter name="browser" value="firefox"/>
        <classes>
            <class name="tests.SmokeTests"/>
        </classes>
    </test>

    <!-- Mobile Tests -->
    <test name="Mobile Smoke Tests" preserve-order="true" enabled="true">
        <parameter name="browser" value="mobile"/>
        <classes>
            <class name="tests.SmokeTests"/>
        </classes>
    </test>

</suite>
