{"format": 1, "restore": {"/home/<USER>/personal/Testing/wtc-user-journey-testing/test/test.csproj": {}}, "projects": {"/home/<USER>/personal/Testing/wtc-user-journey-testing/test/test.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/personal/Testing/wtc-user-journey-testing/test/test.csproj", "projectName": "test", "projectPath": "/home/<USER>/personal/Testing/wtc-user-journey-testing/test/test.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/personal/Testing/wtc-user-journey-testing/test/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.6.0, )"}, "NUnit": {"target": "Package", "version": "[3.13.3, )"}, "NUnit.Analyzers": {"target": "Package", "version": "[3.6.1, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[4.2.1, )"}, "Selenium.WebDriver": {"target": "Package", "version": "[4.35.0, )"}, "WebDriverManager": {"target": "Package", "version": "[2.17.6, )"}, "coverlet.collector": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.117/PortableRuntimeIdentifierGraph.json"}}}}}