{"version": 2, "dgSpecHash": "R6/FSW38RyOgeai4Aj6dkys7UmuI76A2sNMrWsSM8mgzaWrcNyaMXYBW2Vk0zAKLXvxuF1KTw86wKnPYOf4cgw==", "success": true, "projectFilePath": "/home/<USER>/personal/Testing/wtc-user-journey-testing/test/test.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/anglesharp/1.1.2/anglesharp.1.1.2.nupkg.sha512", "/home/<USER>/.nuget/packages/coverlet.collector/6.0.0/coverlet.collector.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.codecoverage/17.6.0/microsoft.codecoverage.17.6.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.net.test.sdk/17.6.0/microsoft.net.test.sdk.17.6.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.netcore.platforms/5.0.0/microsoft.netcore.platforms.5.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.6.0/microsoft.testplatform.objectmodel.17.6.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.6.0/microsoft.testplatform.testhost.17.6.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.win32.registry/5.0.0/microsoft.win32.registry.5.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/netstandard.library/2.0.0/netstandard.library.2.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/newtonsoft.json/13.0.3/newtonsoft.json.13.0.3.nupkg.sha512", "/home/<USER>/.nuget/packages/nuget.frameworks/5.11.0/nuget.frameworks.5.11.0.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit/3.13.3/nunit.3.13.3.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit.analyzers/3.6.1/nunit.analyzers.3.6.1.nupkg.sha512", "/home/<USER>/.nuget/packages/nunit3testadapter/4.2.1/nunit3testadapter.4.2.1.nupkg.sha512", "/home/<USER>/.nuget/packages/selenium.webdriver/4.35.0/selenium.webdriver.4.35.0.nupkg.sha512", "/home/<USER>/.nuget/packages/sharpziplib/1.4.2/sharpziplib.1.4.2.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reflection.metadata/1.6.0/system.reflection.metadata.1.6.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.security.accesscontrol/5.0.0/system.security.accesscontrol.5.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.security.principal.windows/5.0.0/system.security.principal.windows.5.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.encoding.codepages/8.0.0/system.text.encoding.codepages.8.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/webdrivermanager/2.17.6/webdrivermanager.2.17.6.nupkg.sha512"], "logs": []}